/**
 * Trainee Profile JavaScript
 * Handles file upload interactions and enhanced UX
 */

jQuery(document).ready(function($) {
    'use strict';

    // File upload functionality
    initFileUploads();

    /**
     * Initialize file upload functionality
     */
    function initFileUploads() {
        // Handle replace file button clicks
        $(document).on('click', '.replace-file-btn', function(e) {
            e.preventDefault();
            var container = $(this).closest('.file-upload-container');
            container.find('.current-file').hide();
            container.find('.file-upload-field').show();
        });

        // Handle cancel replace button clicks
        $(document).on('click', '.cancel-replace-btn', function(e) {
            e.preventDefault();
            var container = $(this).closest('.file-upload-container');
            container.find('.file-upload-field').hide();
            container.find('.current-file').show();
            // Clear the file input
            container.find('.file-input').val('');
        });

        // Handle file input changes for preview
        $(document).on('change', '.file-input', function() {
            var input = this;
            var container = $(input).closest('.file-upload-container');
            var label = container.find('.file-upload-label');
            
            if (input.files && input.files[0]) {
                var file = input.files[0];
                var fileName = file.name;
                var fileSize = (file.size / 1024 / 1024).toFixed(2); // Convert to MB
                
                // Validate file size (5MB max)
                if (file.size > 5 * 1024 * 1024) {
                    alert('File size must be less than 5MB. Selected file is ' + fileSize + 'MB.');
                    $(input).val('');
                    return;
                }
                
                // Validate file type
                var allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'application/pdf'];
                if (allowedTypes.indexOf(file.type) === -1) {
                    alert('Please select a valid file type (JPG, PNG, GIF, or PDF).');
                    $(input).val('');
                    return;
                }
                
                // Update label text
                label.html('<span class="dashicons dashicons-yes"></span>Selected: ' + fileName + ' (' + fileSize + 'MB)');
                label.addClass('file-selected');
                
                // Show preview for images
                if (file.type.startsWith('image/')) {
                    showImagePreview(file, container);
                }
            } else {
                // Reset label
                var fieldName = container.data('field');
                var labelText = fieldName === 'medical_certificate' ? 'Medical Certificate' : 'ID Picture';
                label.html('<span class="dashicons dashicons-upload"></span>Choose ' + labelText + ' (JPG, PNG, GIF, PDF - Max 5MB)');
                label.removeClass('file-selected');
                
                // Remove preview
                container.find('.file-preview-new').remove();
            }
        });

        // Drag and drop functionality
        $('.file-upload-label').on('dragover', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).addClass('drag-over');
        });

        $('.file-upload-label').on('dragleave', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).removeClass('drag-over');
        });

        $('.file-upload-label').on('drop', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).removeClass('drag-over');
            
            var files = e.originalEvent.dataTransfer.files;
            if (files.length > 0) {
                var input = $(this).siblings('.file-input')[0];
                input.files = files;
                $(input).trigger('change');
            }
        });
    }

    /**
     * Show image preview
     */
    function showImagePreview(file, container) {
        var reader = new FileReader();
        reader.onload = function(e) {
            // Remove existing preview
            container.find('.file-preview-new').remove();
            
            // Create new preview
            var preview = $('<div class="file-preview-new" style="margin-top: 10px;">' +
                '<img src="' + e.target.result + '" alt="Preview" style="max-width: 150px; max-height: 150px; border: 1px solid #ddd; border-radius: 4px;">' +
                '<p style="font-size: 12px; color: #666; margin: 5px 0 0 0;">Preview</p>' +
                '</div>');
            
            container.find('.file-upload-field').append(preview);
        };
        reader.readAsDataURL(file);
    }

    /**
     * Form validation before submit
     */
    $('form').on('submit', function(e) {
        var hasLargeFiles = false;
        var errorMessage = '';
        
        $('.file-input').each(function() {
            if (this.files && this.files[0]) {
                var file = this.files[0];
                var fileSize = file.size / 1024 / 1024; // Convert to MB
                
                if (fileSize > 5) {
                    hasLargeFiles = true;
                    var fieldName = $(this).closest('.file-upload-container').data('field');
                    var labelText = fieldName === 'medical_certificate' ? 'Medical Certificate' : 'ID Picture';
                    errorMessage += labelText + ' file size (' + fileSize.toFixed(2) + 'MB) exceeds 5MB limit.\n';
                }
            }
        });
        
        if (hasLargeFiles) {
            e.preventDefault();
            alert('Please fix the following errors:\n\n' + errorMessage);
            return false;
        }
    });

    /**
     * Add loading state during form submission
     */
    $('form').on('submit', function() {
        var submitBtn = $(this).find('input[type="submit"]');
        submitBtn.val('Saving...').prop('disabled', true);
        
        // Re-enable after 10 seconds as fallback
        setTimeout(function() {
            submitBtn.val('Save Changes').prop('disabled', false);
        }, 10000);
    });
});
