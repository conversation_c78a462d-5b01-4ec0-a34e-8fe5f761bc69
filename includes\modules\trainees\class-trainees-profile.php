<?php

/**
 * The Trainees Profile Management
 *
 * @package    Learnpressium
 * @subpackage Learnpressium/includes/modules/trainees
 */

/**
 * The Trainees Profile class.
 *
 * Handles individual trainee profile display and editing functionality.
 */
class Trainees_Profile {

    /**
     * Display the trainee profile page
     */
    public function display_profile_page() {
        // Check user capabilities
        if (!current_user_can('manage_options')) {
            return;
        }

        // Get the username from the URL
        $username = isset($_GET['username']) ? sanitize_user($_GET['username']) : '';

        if (empty($username)) {
            wp_die('No username specified.');
        }

        // Get the user by username
        $user = get_user_by('login', $username);

        if (!$user) {
            wp_die('User not found.');
        }

        // Handle form submission
        $message = '';
        $message_type = '';

        if (isset($_POST['save_trainee_profile']) && isset($_POST['trainee_profile_nonce']) && wp_verify_nonce($_POST['trainee_profile_nonce'], 'save_trainee_profile_' . $user->ID)) {
            // Update user data
            $user_data = array(
                'ID' => $user->ID,
                'first_name' => sanitize_text_field($_POST['first_name'] ?? ''),
                'last_name' => sanitize_text_field($_POST['last_name'] ?? ''),
                'user_email' => sanitize_email($_POST['email'] ?? '')
            );

            $result = wp_update_user($user_data);

            if (is_wp_error($result)) {
                $message = $result->get_error_message();
                $message_type = 'error';
            } else {
                // Update user meta
                update_user_meta($user->ID, 'middle_name', sanitize_text_field($_POST['middle_name'] ?? ''));
                update_user_meta($user->ID, 'name_extension', sanitize_text_field($_POST['name_extension'] ?? ''));
                update_user_meta($user->ID, 'gender', sanitize_text_field($_POST['gender'] ?? ''));
                update_user_meta($user->ID, 'civil_status', sanitize_text_field($_POST['civil_status'] ?? ''));
                update_user_meta($user->ID, 'srn', sanitize_text_field($_POST['srn'] ?? ''));
                update_user_meta($user->ID, 'nationality', sanitize_text_field($_POST['nationality'] ?? ''));
                update_user_meta($user->ID, 'date_of_birth', sanitize_text_field($_POST['date_of_birth'] ?? ''));
                update_user_meta($user->ID, 'place_of_birth', sanitize_text_field($_POST['place_of_birth'] ?? ''));
                update_user_meta($user->ID, 'complete_address', sanitize_textarea_field($_POST['complete_address'] ?? ''));
                update_user_meta($user->ID, 'phone_number', sanitize_text_field($_POST['phone_number'] ?? ''));
                update_user_meta($user->ID, 'coupon_code', sanitize_text_field($_POST['coupon_code'] ?? ''));

                // Emergency contact information
                update_user_meta($user->ID, 'emergency_first_name', sanitize_text_field($_POST['emergency_first_name'] ?? ''));
                update_user_meta($user->ID, 'emergency_middle_name', sanitize_text_field($_POST['emergency_middle_name'] ?? ''));
                update_user_meta($user->ID, 'emergency_last_name', sanitize_text_field($_POST['emergency_last_name'] ?? ''));
                update_user_meta($user->ID, 'emergency_contact', sanitize_text_field($_POST['emergency_contact'] ?? ''));
                update_user_meta($user->ID, 'emergency_relationship', sanitize_text_field($_POST['emergency_relationship'] ?? ''));
                update_user_meta($user->ID, 'emergency_address', sanitize_textarea_field($_POST['emergency_address'] ?? ''));

                // Handle file uploads
                $this->handle_file_uploads($user->ID);

                $message = 'Profile updated successfully.';
                $message_type = 'success';

                // Refresh user data
                $user = get_user_by('id', $user->ID);
            }
        }

        // Get user's name
        $first_name = get_user_meta($user->ID, 'first_name', true);
        $last_name = get_user_meta($user->ID, 'last_name', true);

        if (!empty($first_name) && !empty($last_name)) {
            $full_name = $first_name . ' ' . $last_name;
        } else {
            $full_name = $user->user_login;
        }

        // Enqueue scripts and styles for file uploads
        $this->enqueue_profile_assets();

        ?>
        <div class="wrap">
            <h1><?php echo esc_html($full_name); ?></h1>
            <p>
                <a href="<?php echo esc_url(admin_url('admin.php?page=trainees')); ?>" class="button">
                    &laquo; Back to Trainees List
                </a>
            </p>

            <?php if (!empty($message)): ?>
                <div class="notice notice-<?php echo $message_type; ?> is-dismissible">
                    <p><?php echo esc_html($message); ?></p>
                </div>
            <?php endif; ?>

            <div class="trainee-profile-content">
                <?php
                // Include the trainee page content
                if (function_exists('crf_show_registration_details')) {
                    // If the function already exists, call it directly
                    crf_show_registration_details($user);
                } else {
                    // If the function doesn't exist, we need to define an editable version
                    $this->render_profile_form($user);
                }
                ?>
            </div>

        </div>
        <?php
    }

    /**
     * Public method to enqueue profile scripts (called by admin_enqueue_scripts hook)
     */
    public function enqueue_profile_scripts($hook) {
        // Only load on trainee profile page
        if ($hook !== 'admin_page_trainee-profile') {
            return;
        }

        $this->enqueue_profile_assets();
    }

    /**
     * Enqueue assets for profile page
     */
    private function enqueue_profile_assets() {
        // Enqueue JavaScript
        wp_enqueue_script(
            'trainee-profile-js',
            LEARNPRESSIUM_PLUGIN_URL . 'admin/js/trainee-profile.js',
            array('jquery'),
            LEARNPRESSIUM_VERSION,
            true
        );

        // Add CSS for drag and drop styling and new features
        wp_add_inline_style('learnpressium-admin', '
            .file-upload-label.drag-over {
                background: #005a87 !important;
                transform: scale(1.02);
            }
            .file-upload-label.file-selected {
                background: #46b450 !important;
            }
            .legacy-file-badge {
                background: #ff9800;
                color: white;
                padding: 2px 6px;
                border-radius: 3px;
                font-size: 10px;
                font-weight: bold;
                margin-left: 8px;
                text-transform: uppercase;
            }
            .migrate-file-btn {
                background: #2196f3 !important;
                border-color: #2196f3 !important;
                color: white !important;
            }
            .migrate-file-btn:hover {
                background: #1976d2 !important;
                border-color: #1976d2 !important;
            }
        ');

        // Localize script for AJAX
        wp_localize_script('trainee-profile-js', 'traineeProfile', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('migrate_trainee_file'),
            'strings' => array(
                'migrating' => 'Migrating file...',
                'migrateSuccess' => 'File migrated successfully to WordPress Media Library!',
                'migrateError' => 'Failed to migrate file. Please try again.',
                'confirmMigrate' => 'Are you sure you want to migrate this file to WordPress Media Library? This action cannot be undone.'
            )
        ));
    }

    /**
     * Initialize hooks for AJAX and other functionality
     */
    public function init_hooks() {
        // AJAX hooks for file migration
        add_action('wp_ajax_migrate_trainee_file', array($this, 'ajax_migrate_file'));

        // Admin notices for legacy file detection
        add_action('admin_notices', array($this, 'legacy_files_admin_notice'));
    }

    /**
     * Display admin notice if legacy files are detected
     */
    public function legacy_files_admin_notice() {
        // Only show on trainees pages
        $screen = get_current_screen();
        if (!$screen || !in_array($screen->id, array('toplevel_page_trainees', 'admin_page_trainee-profile'))) {
            return;
        }

        // Check if there are any legacy files
        if ($this->has_legacy_files()) {
            ?>
            <div class="notice notice-warning is-dismissible">
                <p>
                    <strong>Legacy Files Detected:</strong> Some trainee files are using the old storage system.
                    <a href="#" onclick="alert('Individual files can be migrated using the \'Migrate to Media Library\' button on each trainee profile, or contact your administrator for bulk migration.')">
                        Learn more about migrating to WordPress Media Library
                    </a>
                </p>
            </div>
            <?php
        }
    }

    /**
     * Check if there are any legacy files in the system
     */
    private function has_legacy_files() {
        global $wpdb;

        // Check for user meta with legacy file structure
        $legacy_count = $wpdb->get_var($wpdb->prepare("
            SELECT COUNT(*)
            FROM {$wpdb->usermeta}
            WHERE meta_key IN ('medical_certificate', 'id_picture')
            AND meta_value LIKE %s
            AND user_id NOT IN (
                SELECT DISTINCT user_id
                FROM {$wpdb->usermeta}
                WHERE meta_key IN ('medical_certificate_attachment_id', 'id_picture_attachment_id')
                AND meta_value != ''
            )
        ", '%file_path%'));

        return $legacy_count > 0;
    }

    /**
     * Render the profile form
     */
    private function render_profile_form($user) {
        ?>
        <form method="post" action="" enctype="multipart/form-data">
            <?php wp_nonce_field('save_trainee_profile_' . $user->ID, 'trainee_profile_nonce'); ?>
            <h2>Registration Details</h2>
            <table class="form-table">
                <!-- Personal Information -->
                <tr>
                    <th colspan="2"><h3>Personal Information</h3></th>
                </tr>
                <tr>
                    <th><label for="first_name">First Name</label></th>
                    <td>
                        <input type="text" name="first_name" id="first_name"
                               value="<?php echo esc_attr(get_user_meta($user->ID, 'first_name', true)); ?>"
                               class="regular-text" />
                    </td>
                </tr>
                <tr>
                    <th><label for="middle_name">Middle Name</label></th>
                    <td>
                        <input type="text" name="middle_name" id="middle_name"
                               value="<?php echo esc_attr(get_user_meta($user->ID, 'middle_name', true)); ?>"
                               class="regular-text" />
                    </td>
                </tr>
                <tr>
                    <th><label for="last_name">Last Name</label></th>
                    <td>
                        <input type="text" name="last_name" id="last_name"
                               value="<?php echo esc_attr(get_user_meta($user->ID, 'last_name', true)); ?>"
                               class="regular-text" />
                    </td>
                </tr>
                <tr>
                    <th><label for="name_extension">Name Extension</label></th>
                    <td>
                        <input type="text" name="name_extension" id="name_extension"
                               value="<?php echo esc_attr(get_user_meta($user->ID, 'name_extension', true)); ?>"
                               class="regular-text" />
                    </td>
                </tr>
                <tr>
                    <th><label for="gender">Gender</label></th>
                    <td>
                        <select name="gender" id="gender" class="regular-text">
                            <option value="">Select Gender</option>
                            <option value="Male" <?php selected(get_user_meta($user->ID, 'gender', true), 'Male'); ?>>Male</option>
                            <option value="Female" <?php selected(get_user_meta($user->ID, 'gender', true), 'Female'); ?>>Female</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <th><label for="civil_status">Civil Status</label></th>
                    <td>
                        <select name="civil_status" id="civil_status" class="regular-text">
                            <option value="">Select Civil Status</option>
                            <option value="Single" <?php selected(get_user_meta($user->ID, 'civil_status', true), 'Single'); ?>>Single</option>
                            <option value="Married" <?php selected(get_user_meta($user->ID, 'civil_status', true), 'Married'); ?>>Married</option>
                            <option value="Divorced" <?php selected(get_user_meta($user->ID, 'civil_status', true), 'Divorced'); ?>>Divorced</option>
                            <option value="Widowed" <?php selected(get_user_meta($user->ID, 'civil_status', true), 'Widowed'); ?>>Widowed</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <th><label for="srn">SRN</label></th>
                    <td>
                        <input type="text" name="srn" id="srn"
                               value="<?php echo esc_attr(get_user_meta($user->ID, 'srn', true)); ?>"
                               class="regular-text" />
                    </td>
                </tr>
                <tr>
                    <th><label for="nationality">Nationality</label></th>
                    <td>
                        <input type="text" name="nationality" id="nationality"
                               value="<?php echo esc_attr(get_user_meta($user->ID, 'nationality', true)); ?>"
                               class="regular-text" />
                    </td>
                </tr>
                <tr>
                    <th><label for="date_of_birth">Date of Birth</label></th>
                    <td>
                        <input type="date" name="date_of_birth" id="date_of_birth"
                               value="<?php echo esc_attr(get_user_meta($user->ID, 'date_of_birth', true)); ?>"
                               class="regular-text" />
                    </td>
                </tr>
                <tr>
                    <th><label for="place_of_birth">Place of Birth</label></th>
                    <td>
                        <input type="text" name="place_of_birth" id="place_of_birth"
                               value="<?php echo esc_attr(get_user_meta($user->ID, 'place_of_birth', true)); ?>"
                               class="regular-text" />
                    </td>
                </tr>
                <tr>
                    <th><label for="complete_address">Complete Address</label></th>
                    <td>
                        <textarea name="complete_address" id="complete_address"
                                  class="regular-text" rows="3"><?php echo esc_textarea(get_user_meta($user->ID, 'complete_address', true)); ?></textarea>
                    </td>
                </tr>

                <!-- Contact Information -->
                <tr>
                    <th colspan="2"><h3>Contact Information</h3></th>
                </tr>
                <tr>
                    <th><label for="email">Email</label></th>
                    <td>
                        <input type="email" name="email" id="email"
                               value="<?php echo esc_attr($user->user_email); ?>"
                               class="regular-text" />
                    </td>
                </tr>
                <tr>
                    <th><label for="phone_number">Phone Number</label></th>
                    <td>
                        <input type="text" name="phone_number" id="phone_number"
                               value="<?php echo esc_attr(get_user_meta($user->ID, 'phone_number', true)); ?>"
                               class="regular-text" />
                    </td>
                </tr>

                <!-- Username & Account -->
                <tr>
                    <th colspan="2"><h3>Username & Account</h3></th>
                </tr>
                <tr>
                    <th><label for="username">Username</label></th>
                    <td>
                        <input type="text" id="username" value="<?php echo esc_attr($user->user_login); ?>" class="regular-text" disabled />
                        <p class="description">Username cannot be changed.</p>
                    </td>
                </tr>

                <!-- Marketing -->
                <tr>
                    <th colspan="2"><h3>Marketing</h3></th>
                </tr>
                <tr>
                    <th><label for="coupon_code">Coupon Code</label></th>
                    <td>
                        <input type="text" name="coupon_code" id="coupon_code"
                               value="<?php echo esc_attr(get_user_meta($user->ID, 'coupon_code', true)); ?>"
                               class="regular-text" />
                    </td>
                </tr>

                <!-- Emergency Contact -->
                <tr>
                    <th colspan="2"><h3>In Case of Emergency</h3></th>
                </tr>
                <tr>
                    <th><label for="emergency_first_name">First Name</label></th>
                    <td>
                        <input type="text" name="emergency_first_name" id="emergency_first_name"
                               value="<?php echo esc_attr(get_user_meta($user->ID, 'emergency_first_name', true)); ?>"
                               class="regular-text" />
                    </td>
                </tr>
                <tr>
                    <th><label for="emergency_middle_name">Middle Name</label></th>
                    <td>
                        <input type="text" name="emergency_middle_name" id="emergency_middle_name"
                               value="<?php echo esc_attr(get_user_meta($user->ID, 'emergency_middle_name', true)); ?>"
                               class="regular-text" />
                    </td>
                </tr>
                <tr>
                    <th><label for="emergency_last_name">Last Name</label></th>
                    <td>
                        <input type="text" name="emergency_last_name" id="emergency_last_name"
                               value="<?php echo esc_attr(get_user_meta($user->ID, 'emergency_last_name', true)); ?>"
                               class="regular-text" />
                    </td>
                </tr>
                <tr>
                    <th><label for="emergency_contact">Contact Number</label></th>
                    <td>
                        <input type="text" name="emergency_contact" id="emergency_contact"
                               value="<?php echo esc_attr(get_user_meta($user->ID, 'emergency_contact', true)); ?>"
                               class="regular-text" />
                    </td>
                </tr>
                <tr>
                    <th><label for="emergency_relationship">Relationship</label></th>
                    <td>
                        <input type="text" name="emergency_relationship" id="emergency_relationship"
                               value="<?php echo esc_attr(get_user_meta($user->ID, 'emergency_relationship', true)); ?>"
                               class="regular-text" />
                    </td>
                </tr>
                <tr>
                    <th><label for="emergency_address">Complete Address</label></th>
                    <td>
                        <textarea name="emergency_address" id="emergency_address"
                                  class="regular-text" rows="3"><?php echo esc_textarea(get_user_meta($user->ID, 'emergency_address', true)); ?></textarea>
                    </td>
                </tr>

                <!-- Requirements Section -->
                <tr>
                    <th colspan="2"><h3>Requirements</h3></th>
                </tr>
                <tr>
                    <th><label for="medical_certificate">Medical Certificate</label></th>
                    <td>
                        <?php $this->render_file_upload_field($user, 'medical_certificate', 'Medical Certificate'); ?>
                    </td>
                </tr>
                <tr>
                    <th><label for="id_picture">ID Picture</label></th>
                    <td>
                        <?php $this->render_file_upload_field($user, 'id_picture', 'ID Picture'); ?>
                    </td>
                </tr>
            </table>

            <p class="submit">
                <input type="submit" name="save_trainee_profile" id="save_trainee_profile" class="button button-primary" value="Save Changes">
            </p>

        </form>
        <?php
    }

    /**
     * Handle file uploads for requirements
     */
    private function handle_file_uploads($user_id) {
        $upload_fields = array('medical_certificate', 'id_picture');

        foreach ($upload_fields as $field) {
            if (isset($_FILES[$field]) && $_FILES[$field]['error'] === UPLOAD_ERR_OK) {
                $attachment_id = $this->process_file_upload($_FILES[$field], $field, $user_id);
                if ($attachment_id) {
                    // Delete old attachment if it exists
                    $old_attachment_id = get_user_meta($user_id, $field . '_attachment_id', true);
                    if ($old_attachment_id) {
                        wp_delete_attachment($old_attachment_id, true);
                    }

                    // Store the new attachment ID
                    update_user_meta($user_id, $field . '_attachment_id', $attachment_id);

                    // Also store some metadata for easy access
                    $attachment_data = array(
                        'attachment_id' => $attachment_id,
                        'url' => wp_get_attachment_url($attachment_id),
                        'filename' => get_the_title($attachment_id),
                        'upload_date' => current_time('mysql'),
                        'file_type' => get_post_mime_type($attachment_id)
                    );
                    update_user_meta($user_id, $field, $attachment_data);
                }
            }
        }
    }

    /**
     * Process individual file upload using WordPress media library
     */
    private function process_file_upload($file, $field_name, $user_id) {
        // Validate file type
        $allowed_types = array(
            'image/jpeg', 'image/jpg', 'image/png', 'image/gif',
            'application/pdf'
        );

        if (!in_array($file['type'], $allowed_types)) {
            return false;
        }

        // Validate file size (5MB max)
        if ($file['size'] > 5 * 1024 * 1024) {
            return false;
        }

        // Include WordPress file handling functions
        if (!function_exists('wp_handle_upload')) {
            require_once(ABSPATH . 'wp-admin/includes/file.php');
        }
        if (!function_exists('wp_generate_attachment_metadata')) {
            require_once(ABSPATH . 'wp-admin/includes/image.php');
        }

        // Set up upload overrides
        $upload_overrides = array(
            'test_form' => false,
            'test_size' => true,
            'test_upload' => true,
        );

        // Handle the upload using WordPress functions
        $uploaded_file = wp_handle_upload($file, $upload_overrides);

        if (isset($uploaded_file['error'])) {
            return false;
        }

        // Prepare attachment data
        $file_path = $uploaded_file['file'];
        $file_url = $uploaded_file['url'];
        $file_type = $uploaded_file['type'];

        // Get user info for better file naming
        $user = get_user_by('id', $user_id);
        $user_name = $user->first_name && $user->last_name
            ? $user->first_name . ' ' . $user->last_name
            : $user->user_login;

        // Create meaningful title for the attachment
        $field_label = ($field_name === 'medical_certificate') ? 'Medical Certificate' : 'ID Picture';
        $attachment_title = sprintf('%s - %s (%s)', $user_name, $field_label, date('Y-m-d'));

        // Prepare attachment post data
        $attachment = array(
            'guid' => $file_url,
            'post_mime_type' => $file_type,
            'post_title' => $attachment_title,
            'post_content' => sprintf('Trainee requirement: %s for %s (User ID: %d)', $field_label, $user_name, $user_id),
            'post_status' => 'inherit',
            'post_author' => get_current_user_id(),
        );

        // Insert the attachment into the media library
        $attachment_id = wp_insert_attachment($attachment, $file_path);

        if (is_wp_error($attachment_id)) {
            return false;
        }

        // Generate attachment metadata and update
        $attachment_data = wp_generate_attachment_metadata($attachment_id, $file_path);
        wp_update_attachment_metadata($attachment_id, $attachment_data);

        // Add custom meta to link this attachment to the trainee
        update_post_meta($attachment_id, '_trainee_user_id', $user_id);
        update_post_meta($attachment_id, '_trainee_requirement_type', $field_name);
        update_post_meta($attachment_id, '_trainee_upload_date', current_time('mysql'));

        return $attachment_id;
    }

    /**
     * Render file upload field with preview - Updated for WordPress Media Library
     */
    private function render_file_upload_field($user, $field_name, $label) {
        // Try to get attachment ID first (new system)
        $attachment_id = get_user_meta($user->ID, $field_name . '_attachment_id', true);
        $current_file = null;

        if ($attachment_id && get_post($attachment_id)) {
            // New system: Get data from attachment
            $current_file = array(
                'attachment_id' => $attachment_id,
                'file_url' => wp_get_attachment_url($attachment_id),
                'original_name' => get_the_title($attachment_id),
                'file_type' => get_post_mime_type($attachment_id),
                'upload_date' => get_the_date('Y-m-d H:i:s', $attachment_id),
                'file_size' => $this->get_attachment_file_size($attachment_id)
            );
        } else {
            // Fallback: Check for old system data
            $old_file_data = get_user_meta($user->ID, $field_name, true);
            if ($old_file_data && is_array($old_file_data) && isset($old_file_data['file_url'])) {
                $current_file = $old_file_data;
                $current_file['is_legacy'] = true; // Mark as legacy file
            }
        }
        ?>
        <div class="file-upload-container" data-field="<?php echo esc_attr($field_name); ?>">
            <?php if ($current_file): ?>
                <div class="current-file">
                    <div class="file-preview">
                        <?php if (strpos($current_file['file_type'], 'image/') === 0): ?>
                            <img src="<?php echo esc_url($current_file['file_url']); ?>"
                                 alt="<?php echo esc_attr($label); ?>"
                                 class="file-thumbnail" />
                        <?php else: ?>
                            <div class="file-icon pdf-icon">
                                <span class="dashicons dashicons-media-document"></span>
                                <span class="file-type">PDF</span>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="file-info">
                        <p class="file-name">
                            <?php echo esc_html($current_file['original_name']); ?>
                            <?php if (isset($current_file['is_legacy'])): ?>
                                <span class="legacy-file-badge" title="This file was uploaded with the old system">Legacy</span>
                            <?php endif; ?>
                        </p>
                        <p class="file-date">
                            Uploaded: <?php echo esc_html(date('M j, Y', strtotime($current_file['upload_date']))); ?>
                            <?php if (isset($current_file['file_size'])): ?>
                                <span class="file-size">(<?php echo esc_html($current_file['file_size']); ?>)</span>
                            <?php endif; ?>
                        </p>
                        <div class="file-actions">
                            <a href="<?php echo esc_url($current_file['file_url']); ?>"
                               target="_blank"
                               class="button button-small">View/Download</a>
                            <?php if (isset($current_file['attachment_id'])): ?>
                                <a href="<?php echo esc_url(admin_url('post.php?post=' . $current_file['attachment_id'] . '&action=edit')); ?>"
                                   target="_blank"
                                   class="button button-small">Edit in Media Library</a>
                            <?php endif; ?>
                            <button type="button"
                                    class="button button-small replace-file-btn"
                                    data-field="<?php echo esc_attr($field_name); ?>">Replace</button>
                            <?php if (isset($current_file['is_legacy'])): ?>
                                <button type="button"
                                        class="button button-small migrate-file-btn"
                                        data-field="<?php echo esc_attr($field_name); ?>"
                                        data-user-id="<?php echo esc_attr($user->ID); ?>"
                                        title="Migrate this file to WordPress Media Library">Migrate to Media Library</button>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="file-upload-field" style="display: none;">
            <?php else: ?>
                <div class="file-upload-field">
            <?php endif; ?>
                <input type="file"
                       name="<?php echo esc_attr($field_name); ?>"
                       id="<?php echo esc_attr($field_name); ?>"
                       accept=".jpg,.jpeg,.png,.gif,.pdf"
                       class="file-input" />
                <label for="<?php echo esc_attr($field_name); ?>" class="file-upload-label">
                    <span class="dashicons dashicons-upload"></span>
                    Choose <?php echo esc_html($label); ?> (JPG, PNG, GIF, PDF - Max 5MB)
                </label>
                <?php if ($current_file): ?>
                    <button type="button" class="button button-small cancel-replace-btn">Cancel</button>
                <?php endif; ?>
            </div>
        </div>
        <?php
    }

    /**
     * Get human-readable file size for attachment
     */
    private function get_attachment_file_size($attachment_id) {
        $file_path = get_attached_file($attachment_id);
        if ($file_path && file_exists($file_path)) {
            $bytes = filesize($file_path);
            $units = array('B', 'KB', 'MB', 'GB');

            for ($i = 0; $bytes > 1024; $i++) {
                $bytes /= 1024;
            }

            return round($bytes, 2) . ' ' . $units[$i];
        }
        return 'Unknown';
    }

    /**
     * Migrate legacy file to WordPress Media Library
     * This function can be called via AJAX or directly
     */
    public function migrate_legacy_file($user_id, $field_name) {
        // Get the legacy file data
        $legacy_file = get_user_meta($user_id, $field_name, true);

        if (!$legacy_file || !is_array($legacy_file) || !isset($legacy_file['file_path'])) {
            return false;
        }

        // Check if file still exists
        if (!file_exists($legacy_file['file_path'])) {
            return false;
        }

        // Include WordPress file handling functions
        if (!function_exists('wp_insert_attachment')) {
            require_once(ABSPATH . 'wp-admin/includes/file.php');
            require_once(ABSPATH . 'wp-admin/includes/image.php');
        }

        // Get user info for better file naming
        $user = get_user_by('id', $user_id);
        $user_name = $user->first_name && $user->last_name
            ? $user->first_name . ' ' . $user->last_name
            : $user->user_login;

        // Create meaningful title for the attachment
        $field_label = ($field_name === 'medical_certificate') ? 'Medical Certificate' : 'ID Picture';
        $attachment_title = sprintf('%s - %s (Migrated %s)', $user_name, $field_label, date('Y-m-d'));

        // Copy file to WordPress uploads directory
        $upload_dir = wp_upload_dir();
        $file_extension = pathinfo($legacy_file['file_path'], PATHINFO_EXTENSION);
        $new_filename = sanitize_file_name($attachment_title . '.' . $file_extension);
        $new_file_path = $upload_dir['path'] . '/' . $new_filename;

        // Copy the file
        if (!copy($legacy_file['file_path'], $new_file_path)) {
            return false;
        }

        // Prepare attachment post data
        $attachment = array(
            'guid' => $upload_dir['url'] . '/' . $new_filename,
            'post_mime_type' => $legacy_file['file_type'],
            'post_title' => $attachment_title,
            'post_content' => sprintf('Migrated trainee requirement: %s for %s (User ID: %d)', $field_label, $user_name, $user_id),
            'post_status' => 'inherit',
            'post_author' => get_current_user_id(),
        );

        // Insert the attachment into the media library
        $attachment_id = wp_insert_attachment($attachment, $new_file_path);

        if (is_wp_error($attachment_id)) {
            // Clean up the copied file if attachment creation failed
            unlink($new_file_path);
            return false;
        }

        // Generate attachment metadata and update
        $attachment_data = wp_generate_attachment_metadata($attachment_id, $new_file_path);
        wp_update_attachment_metadata($attachment_id, $attachment_data);

        // Add custom meta to link this attachment to the trainee
        update_post_meta($attachment_id, '_trainee_user_id', $user_id);
        update_post_meta($attachment_id, '_trainee_requirement_type', $field_name);
        update_post_meta($attachment_id, '_trainee_upload_date', $legacy_file['upload_date']);
        update_post_meta($attachment_id, '_migrated_from_legacy', true);

        // Update user meta with new attachment ID
        update_user_meta($user_id, $field_name . '_attachment_id', $attachment_id);

        // Update the file data array to new format
        $new_file_data = array(
            'attachment_id' => $attachment_id,
            'url' => wp_get_attachment_url($attachment_id),
            'filename' => get_the_title($attachment_id),
            'upload_date' => $legacy_file['upload_date'],
            'file_type' => get_post_mime_type($attachment_id),
            'migrated' => true
        );
        update_user_meta($user_id, $field_name, $new_file_data);

        return $attachment_id;
    }

    /**
     * AJAX handler for file migration
     */
    public function ajax_migrate_file() {
        // Verify nonce and permissions
        if (!wp_verify_nonce($_POST['nonce'], 'migrate_trainee_file') || !current_user_can('manage_options')) {
            wp_die('Security check failed');
        }

        $user_id = intval($_POST['user_id']);
        $field_name = sanitize_text_field($_POST['field_name']);

        $result = $this->migrate_legacy_file($user_id, $field_name);

        if ($result) {
            wp_send_json_success(array(
                'message' => 'File migrated successfully to WordPress Media Library',
                'attachment_id' => $result
            ));
        } else {
            wp_send_json_error(array(
                'message' => 'Failed to migrate file'
            ));
        }
    }

    /**
     * Bulk migration script for all existing legacy files
     * This can be called from WordPress admin or via WP-CLI
     */
    public function bulk_migrate_legacy_files() {
        // Get all users with customer role
        $users = get_users(array(
            'role' => 'customer',
            'fields' => 'ID'
        ));

        $migration_results = array(
            'total_users' => count($users),
            'migrated_files' => 0,
            'failed_migrations' => 0,
            'users_processed' => 0,
            'errors' => array()
        );

        $upload_fields = array('medical_certificate', 'id_picture');

        foreach ($users as $user_id) {
            $migration_results['users_processed']++;

            foreach ($upload_fields as $field) {
                // Check if user has legacy file data
                $legacy_file = get_user_meta($user_id, $field, true);

                if ($legacy_file && is_array($legacy_file) && isset($legacy_file['file_path'])) {
                    // Check if already migrated (has attachment_id)
                    $attachment_id = get_user_meta($user_id, $field . '_attachment_id', true);

                    if (!$attachment_id) {
                        // Attempt migration
                        $result = $this->migrate_legacy_file($user_id, $field);

                        if ($result) {
                            $migration_results['migrated_files']++;
                        } else {
                            $migration_results['failed_migrations']++;
                            $migration_results['errors'][] = sprintf(
                                'Failed to migrate %s for user ID %d',
                                $field,
                                $user_id
                            );
                        }
                    }
                }
            }
        }

        return $migration_results;
    }

    /**
     * Admin page for bulk migration (can be added to admin menu if needed)
     */
    public function display_migration_page() {
        if (!current_user_can('manage_options')) {
            wp_die('You do not have sufficient permissions to access this page.');
        }

        if (isset($_POST['run_migration']) && wp_verify_nonce($_POST['migration_nonce'], 'bulk_migrate_files')) {
            $results = $this->bulk_migrate_legacy_files();
            ?>
            <div class="notice notice-success">
                <h3>Migration Complete!</h3>
                <ul>
                    <li>Total users processed: <?php echo esc_html($results['users_processed']); ?></li>
                    <li>Files migrated successfully: <?php echo esc_html($results['migrated_files']); ?></li>
                    <li>Failed migrations: <?php echo esc_html($results['failed_migrations']); ?></li>
                </ul>
                <?php if (!empty($results['errors'])): ?>
                    <h4>Errors:</h4>
                    <ul>
                        <?php foreach ($results['errors'] as $error): ?>
                            <li><?php echo esc_html($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
            </div>
            <?php
        }
        ?>
        <div class="wrap">
            <h1>Migrate Legacy Trainee Files</h1>
            <p>This tool will migrate all existing trainee files from the old custom storage system to WordPress Media Library.</p>

            <div class="notice notice-warning">
                <p><strong>Important:</strong> This migration process will:</p>
                <ul>
                    <li>Copy files from custom directories to WordPress uploads</li>
                    <li>Create proper WordPress attachment records</li>
                    <li>Update user meta to reference attachment IDs</li>
                    <li>Preserve original file information and dates</li>
                </ul>
                <p>It's recommended to backup your database before running this migration.</p>
            </div>

            <form method="post" action="">
                <?php wp_nonce_field('bulk_migrate_files', 'migration_nonce'); ?>
                <p>
                    <input type="submit" name="run_migration" class="button button-primary"
                           value="Run Migration"
                           onclick="return confirm('Are you sure you want to migrate all legacy files? This action cannot be undone.');" />
                </p>
            </form>
        </div>
        <?php
    }

    /**
     * Test function to verify the new system works correctly
     * This can be called from WordPress admin or for debugging
     */
    public function test_media_library_integration() {
        $test_results = array(
            'wordpress_functions' => array(),
            'file_handling' => array(),
            'database_operations' => array(),
            'overall_status' => 'unknown'
        );

        // Test WordPress function availability
        $wp_functions = array(
            'wp_handle_upload', 'wp_insert_attachment', 'wp_generate_attachment_metadata',
            'wp_get_attachment_url', 'get_attached_file', 'wp_delete_attachment'
        );

        foreach ($wp_functions as $function) {
            $test_results['wordpress_functions'][$function] = function_exists($function);
        }

        // Test upload directory permissions
        $upload_dir = wp_upload_dir();
        $test_results['file_handling']['upload_dir_writable'] = is_writable($upload_dir['path']);
        $test_results['file_handling']['upload_dir_path'] = $upload_dir['path'];

        // Test database operations
        global $wpdb;
        $test_results['database_operations']['wp_posts_table'] = $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->posts}'") === $wpdb->posts;
        $test_results['database_operations']['wp_postmeta_table'] = $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->postmeta}'") === $wpdb->postmeta;
        $test_results['database_operations']['wp_usermeta_table'] = $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->usermeta}'") === $wpdb->usermeta;

        // Determine overall status
        $all_wp_functions = array_reduce($test_results['wordpress_functions'], function($carry, $item) {
            return $carry && $item;
        }, true);

        $all_file_handling = array_reduce($test_results['file_handling'], function($carry, $item) {
            return $carry && $item;
        }, true);

        $all_database = array_reduce($test_results['database_operations'], function($carry, $item) {
            return $carry && $item;
        }, true);

        if ($all_wp_functions && $all_file_handling && $all_database) {
            $test_results['overall_status'] = 'ready';
        } else {
            $test_results['overall_status'] = 'issues_detected';
        }

        return $test_results;
    }

    /**
     * Display test results in admin (for debugging)
     */
    public function display_test_results() {
        if (!current_user_can('manage_options')) {
            wp_die('You do not have sufficient permissions to access this page.');
        }

        $results = $this->test_media_library_integration();
        ?>
        <div class="wrap">
            <h1>Media Library Integration Test Results</h1>

            <div class="notice notice-<?php echo $results['overall_status'] === 'ready' ? 'success' : 'warning'; ?>">
                <p><strong>Overall Status:</strong> <?php echo esc_html(ucfirst(str_replace('_', ' ', $results['overall_status']))); ?></p>
            </div>

            <h2>WordPress Functions</h2>
            <table class="widefat">
                <thead>
                    <tr><th>Function</th><th>Available</th></tr>
                </thead>
                <tbody>
                    <?php foreach ($results['wordpress_functions'] as $function => $available): ?>
                        <tr>
                            <td><?php echo esc_html($function); ?></td>
                            <td><?php echo $available ? '✅ Yes' : '❌ No'; ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>

            <h2>File Handling</h2>
            <table class="widefat">
                <thead>
                    <tr><th>Test</th><th>Result</th></tr>
                </thead>
                <tbody>
                    <?php foreach ($results['file_handling'] as $test => $result): ?>
                        <tr>
                            <td><?php echo esc_html(str_replace('_', ' ', ucfirst($test))); ?></td>
                            <td><?php echo is_bool($result) ? ($result ? '✅ Pass' : '❌ Fail') : esc_html($result); ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>

            <h2>Database Operations</h2>
            <table class="widefat">
                <thead>
                    <tr><th>Table</th><th>Available</th></tr>
                </thead>
                <tbody>
                    <?php foreach ($results['database_operations'] as $table => $available): ?>
                        <tr>
                            <td><?php echo esc_html(str_replace('_', ' ', ucfirst($table))); ?></td>
                            <td><?php echo $available ? '✅ Yes' : '❌ No'; ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php
    }
}
