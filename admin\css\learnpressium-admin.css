/* Learnpressium Admin Styles */

/* Trainees Table Styles */
.table-container {
    width: 100%;
    overflow-x: auto;
    margin-bottom: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    border-radius: 4px;
}

.trainees-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
    min-width: 900px; /* Ensures table doesn't shrink too much */
}

.trainees-table th,
.trainees-table td {
    padding: 8px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.trainees-table th {
    background-color: #f9f9f9;
    font-weight: 700;
    font-size: 16px;
}

/* Additional styling for header cells */
.header-cell {
    font-size: 16px;
    font-weight: 500 !important; /* Force boldness */
    text-transform: uppercase; /* Make headers more distinct */
}

.table-title {
    margin-top: 20px;
    margin-bottom: 15px;
    font-size: 24px;
    font-weight: 500;
    color: #23282d;
    text-align: left;
}

.trainees-table tr:hover {
    background-color: #f5f5f5;
}

/* Enrolled Column Styles */
.enrollment-status {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 3px;
    font-weight: bold;
}

.enrollment-status.enrolled {
    background-color: #dff0d8;
    color: #3c763d;
}

.enrollment-status.not-enrolled {
    background-color: #f2dede;
    color: #a94442;
}

/* Enrolled Courses Column Styles */
.enrolled-courses-list {
    margin: 0;
    padding-left: 20px;
}

.enrolled-courses-list li {
    margin-bottom: 4px;
}

.no-courses {
    color: #999;
    font-style: italic;
}

.column-enrolled-courses,
.column-active-enrolled-courses {
    min-width: 200px;
}

.column-active-enrolled {
    min-width: 100px;
}

/* Set ID column to be narrower */
.column-id {
    width: 50px;
    max-width: 50px;
}

/* Download button styles */
.trainees-actions {
    margin: 15px 0;
}

#export-trainees {
    display: inline-flex;
    align-items: center;
}

/* Hover effect for trainee rows */
.trainee-name-container {
    position: relative;
}

.row-actions {
    position: absolute;
    left: -9999px;
    color: #666;
    font-size: 13px;
}

.trainee-row:hover .row-actions {
    position: static;
    left: 0;
    display: inline-block;
    padding-left: 10px;
}

.row-actions .view a {
    color: #0073aa;
    text-decoration: none;
}

.row-actions .view a:hover {
    color: #00a0d2;
    text-decoration: underline;
}

/* File Upload Styles for Trainee Profile */
.file-upload-container {
    margin-bottom: 15px;
}

.current-file {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 15px;
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 10px;
}

.file-preview {
    flex-shrink: 0;
}

.file-thumbnail {
    max-width: 80px;
    max-height: 80px;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.file-icon {
    width: 80px;
    height: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    color: #666;
}

.file-icon .dashicons {
    font-size: 32px;
    width: 32px;
    height: 32px;
}

.file-icon .file-type {
    font-size: 12px;
    font-weight: bold;
    margin-top: 5px;
}

.pdf-icon {
    background: #dc3545;
    color: white;
}

.file-info {
    flex-grow: 1;
}

.file-name {
    font-weight: 600;
    margin: 0 0 5px 0;
    color: #333;
}

.file-date {
    font-size: 13px;
    color: #666;
    margin: 0 0 10px 0;
}

.file-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.file-actions .button {
    margin-bottom: 4px;
}

/* Legacy file badge */
.legacy-file-badge {
    background: #ff9800;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: bold;
    margin-left: 8px;
    text-transform: uppercase;
    display: inline-block;
}

/* Migration button styling */
.migrate-file-btn {
    background: #2196f3 !important;
    border-color: #2196f3 !important;
    color: white !important;
}

.migrate-file-btn:hover {
    background: #1976d2 !important;
    border-color: #1976d2 !important;
}

.migrate-file-btn:disabled {
    background: #ccc !important;
    border-color: #ccc !important;
    cursor: not-allowed;
}

/* File size display */
.file-size {
    color: #666;
    font-size: 12px;
    margin-left: 5px;
}

/* Enhanced file info layout */
.file-info {
    flex: 1;
    min-width: 0; /* Allow text to wrap */
}

.file-name {
    margin: 0 0 5px 0;
    font-weight: 600;
    word-break: break-word;
}

.file-date {
    margin: 0 0 10px 0;
    color: #666;
    font-size: 13px;
}

.file-upload-field {
    position: relative;
}

.file-input {
    position: absolute;
    opacity: 0;
    width: 0.1px;
    height: 0.1px;
    overflow: hidden;
}

.file-upload-label {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    background: #0073aa;
    color: white;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    font-size: 14px;
    border: none;
}

.file-upload-label:hover {
    background: #005a87;
    color: white;
}

.file-upload-label .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

.replace-file-btn {
    background: #f0ad4e;
    border-color: #eea236;
    color: white;
}

.replace-file-btn:hover {
    background: #ec971f;
    border-color: #d58512;
    color: white;
}

.cancel-replace-btn {
    margin-left: 10px;
    background: #6c757d;
    border-color: #6c757d;
    color: white;
}

.cancel-replace-btn:hover {
    background: #5a6268;
    border-color: #545b62;
    color: white;
}

/* Responsive Design for File Uploads */
@media screen and (max-width: 768px) {
    .current-file {
        flex-direction: column;
        gap: 10px;
    }

    .file-actions {
        flex-direction: column;
        gap: 5px;
    }

    .file-actions .button {
        text-align: center;
    }
}
